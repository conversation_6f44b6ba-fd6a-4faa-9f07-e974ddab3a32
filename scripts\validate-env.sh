#!/bin/bash
# 环境变量验证脚本
# 用于验证Docker部署前的环境变量配置

set -e

echo "🔍 验证Docker部署环境变量..."

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo "❌ .env文件不存在，请创建.env文件"
    echo "💡 提示：可以复制.env.example作为模板"
    exit 1
fi

echo "✅ .env文件存在"

# 验证必需的环境变量
REQUIRED_VARS=(
    "POSTGRES_DB"
    "POSTGRES_USER" 
    "POSTGRES_PASSWORD"
    "SECRET_KEY"
    "REDIS_HOST"
)

echo "🔍 检查必需的环境变量..."

# 加载.env文件
source .env

MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        MISSING_VARS+=("$var")
    else
        echo "✅ $var: 已设置"
    fi
done

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
    echo "❌ 以下环境变量未设置："
    for var in "${MISSING_VARS[@]}"; do
        echo "   - $var"
    done
    exit 1
fi

# 验证密码强度
echo "🔍 检查密码强度..."

if [ ${#POSTGRES_PASSWORD} -lt 8 ]; then
    echo "⚠️  警告：POSTGRES_PASSWORD长度少于8位，建议使用更强的密码"
fi

if [ "$SECRET_KEY" = "changethis" ] || [ "$SECRET_KEY" = "your-super-secret-key-change-this-in-production-2024" ]; then
    echo "❌ SECRET_KEY使用默认值，请更改为安全的密钥"
    echo "💡 提示：使用 python -c \"import secrets; print(secrets.token_urlsafe(32))\" 生成"
    exit 1
fi

# 验证Docker环境
echo "🔍 检查Docker环境..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装"
    exit 1
fi

echo "✅ Docker环境正常"

# 验证端口可用性
echo "🔍 检查端口可用性..."

check_port() {
    local port=$1
    local service=$2
    
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo "⚠️  警告：端口 $port ($service) 已被占用"
        return 1
    else
        echo "✅ 端口 $port ($service) 可用"
        return 0
    fi
}

# 检查主要端口
check_port 5432 "PostgreSQL"
check_port 6379 "Redis"
check_port 8000 "测试环境"
check_port 8001 "开发环境"

echo ""
echo "🎉 环境变量验证完成！"
echo ""
echo "📋 部署建议："
echo "   开发环境: docker-compose up -d"
echo "   测试环境: docker-compose -f docker-compose.yml up -d --build"
echo "   扩展部署: docker-compose up -d --scale celery-worker=10"
echo ""
echo "📚 详细文档: docs/DOCKER_USAGE_GUIDE.md"
